{% extends 'base.html' %}
{% load static %}
{% load vite_tags %}

{% block title %}
  Form Builder - PDFlex
{% endblock %}

{% block content %}
  <!-- Form Builder Vue App Container -->
  <div id="form-builder-app" class="min-h-screen">
    <!-- Loading state while Vue app initializes -->
    <div class="flex items-center justify-center min-h-screen bg-gray-50">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p class="text-gray-600">Loading Form Builder...</p>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <!-- Form Builder Data -->
  <script>
    window.formBuilderData = {
      formData: {
        id: '{{ form.id }}',
        name: '{{ form.name|escapejs }}',
        description: '{{ form.description|escapejs }}',
        status: '{{ form.status|escapejs }}',
        fields: {{ form_fields_json|safe }},
        settings: {{ form_settings_json|safe }},
        customization: {{ form_customization_json|safe }}
      },
      csrfToken: '{{ csrf_token }}'
    };
  </script>

  <!-- Form Builder Built Assets -->
  {% load_vite_assets 'form-builder' %}

  <!-- Fallback for browsers without module support -->
  <script nomodule>
    console.warn('Your browser does not support ES modules. Please upgrade to a modern browser.')
  </script>
{% endblock %}
