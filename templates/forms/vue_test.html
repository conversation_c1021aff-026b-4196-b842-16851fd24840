{% extends 'base.html' %}
{% load static %}

{% block title %}
  Vue.js Test - PDFlex
{% endblock %}

{% block content %}
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-4">Vue.js Test Page</h1>
    
    <div class="bg-gray-100 p-4 rounded mb-4">
      <h2 class="text-lg font-semibold mb-2">Basic Vue.js Test</h2>
      <div id="vue-test-app">
        <div class="text-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p class="text-gray-600">Loading Vue.js...</p>
        </div>
      </div>
    </div>
    
    <div class="bg-gray-100 p-4 rounded mb-4">
      <h2 class="text-lg font-semibold mb-2">JavaScript Console Output</h2>
      <div id="console-output" class="bg-white p-2 rounded text-sm font-mono"></div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    // Capture console output
    const originalLog = console.log;
    const originalError = console.error;
    const consoleOutput = document.getElementById('console-output');
    
    function addToConsole(type, message) {
      const div = document.createElement('div');
      div.className = type === 'error' ? 'text-red-600' : 'text-green-600';
      div.textContent = `[${type.toUpperCase()}] ${message}`;
      consoleOutput.appendChild(div);
    }
    
    console.log = function(...args) {
      originalLog.apply(console, args);
      addToConsole('log', args.join(' '));
    };
    
    console.error = function(...args) {
      originalError.apply(console, args);
      addToConsole('error', args.join(' '));
    };
    
    console.log('JavaScript is working');
    console.log('Testing Vue.js import...');
  </script>
  
  <!-- Load Vue.js from CDN for testing -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  
  <script>
    console.log('Vue.js CDN loaded, Vue object:', typeof Vue);
    
    if (typeof Vue !== 'undefined') {
      console.log('Creating Vue app...');
      
      try {
        const { createApp } = Vue;
        
        const app = createApp({
          data() {
            return {
              message: 'Vue.js is working!',
              counter: 0
            }
          },
          mounted() {
            console.log('Vue app mounted successfully');
            this.startCounter();
          },
          methods: {
            startCounter() {
              setInterval(() => {
                this.counter++;
              }, 1000);
            }
          },
          template: `
            <div class="text-center">
              <h3 class="text-xl font-bold text-green-600 mb-4">{{ message }}</h3>
              <p class="text-lg">Counter: {{ counter }}</p>
              <button @click="counter = 0" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                Reset Counter
              </button>
            </div>
          `
        });
        
        app.mount('#vue-test-app');
        console.log('Vue app mounted to #vue-test-app');
        
      } catch (error) {
        console.error('Error creating Vue app:', error);
      }
    } else {
      console.error('Vue.js not available');
    }
  </script>
{% endblock %}
