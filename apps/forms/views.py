from django.shortcuts import render, get_object_or_404, redirect
from django.db import transaction
from django.utils import timezone
from django.utils.text import slugify
from django.contrib import messages
from django.http import JsonResponse
import json
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.contrib.auth import get_user_model
from django.db import models

from .models import (
    FormTemplate, Form, FormField, FormFieldOption,
    FormVersion, FormSubmission, FormShare
)
from .serializers import (
    FormTemplateSerializer, FormTemplateListSerializer,
    FormSerializer, FormListSerializer,
    FormFieldSerializer, FormFieldOptionSerializer,
    FormVersionSerializer, FormSubmissionSerializer,
    FormShareSerializer
)
from .permissions import IsOwnerOrReadOnly, CanManageForm

User = get_user_model()


class FormTemplateViewSet(viewsets.ModelViewSet):
    """ViewSet for form templates."""

    queryset = FormTemplate.objects.all()
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['category', 'is_public', 'is_featured']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at', 'usage_count']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'list':
            return FormTemplateListSerializer
        return FormTemplateSerializer

    def get_queryset(self):
        """Filter templates based on user permissions."""
        user = self.request.user
        queryset = FormTemplate.objects.all()

        if not user.is_staff:
            # Non-staff users can only see public templates or their own
            queryset = queryset.filter(
                models.Q(is_public=True) | models.Q(created_by=user)
            )

        return queryset

    def perform_create(self, serializer):
        """Set the creator when creating a template."""
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['post'])
    def use_template(self, request, pk=None):
        """Create a new form from this template."""
        template = self.get_object()

        # Increment usage count
        template.increment_usage()

        # Create form from template
        form_data = {
            'name': f"Form from {template.name}",
            'description': template.description,
            'template': template,
            'created_by': request.user,
            'settings': template.template_config.get('settings', {}),
        }

        form = Form.objects.create(**form_data)

        # Create fields from template configuration
        fields_config = template.template_config.get('fields', [])
        for field_config in fields_config:
            field_data = {
                'form': form,
                **field_config
            }
            options_data = field_data.pop('options', [])

            field = FormField.objects.create(**field_data)

            # Create field options
            for option_config in options_data:
                FormFieldOption.objects.create(
                    form_field=field,
                    **option_config
                )

        # Create initial version
        FormVersion.create_version(
            form=form,
            change_type=FormVersion.ChangeType.CREATED,
            change_summary=f"Created from template: {template.name}",
            user=request.user
        )

        serializer = FormSerializer(form)
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class FormViewSet(viewsets.ModelViewSet):
    """ViewSet for forms."""

    queryset = Form.objects.all()
    permission_classes = [IsAuthenticated, IsOwnerOrReadOnly]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'is_public', 'submission_handling']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at', 'submission_count']
    ordering = ['-created_at']
    lookup_field = 'slug'

    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'list':
            return FormListSerializer
        return FormSerializer

    def get_queryset(self):
        """Filter forms based on user permissions."""
        user = self.request.user
        queryset = Form.objects.select_related('created_by', 'template').prefetch_related('fields')

        if not user.is_staff:
            # Non-staff users can only see their own forms or shared forms
            queryset = queryset.filter(
                models.Q(created_by=user) |
                models.Q(shares__shared_with=user, shares__is_active=True)
            ).distinct()

        return queryset

    def perform_create(self, serializer):
        """Set the creator when creating a form."""
        form = serializer.save(created_by=self.request.user)

        # Create initial version
        FormVersion.create_version(
            form=form,
            change_type=FormVersion.ChangeType.CREATED,
            change_summary="Initial form creation",
            user=self.request.user
        )

    def perform_update(self, serializer):
        """Create version when updating a form."""
        form = serializer.save()

        # Create version for update
        FormVersion.create_version(
            form=form,
            change_type=FormVersion.ChangeType.UPDATED,
            change_summary="Form updated",
            user=self.request.user
        )

    @action(detail=True, methods=['post'])
    def duplicate(self, request, slug=None):
        """Duplicate a form."""
        original_form = self.get_object()

        with transaction.atomic():
            # Create new form
            new_form = Form.objects.create(
                name=f"{original_form.name} (Copy)",
                description=original_form.description,
                settings=original_form.settings,
                submission_handling=original_form.submission_handling,
                max_submissions=original_form.max_submissions,
                is_public=False,  # Copies are private by default
                requires_authentication=original_form.requires_authentication,
                allowed_domains=original_form.allowed_domains,
                created_by=request.user
            )

            # Duplicate fields
            for field in original_form.fields.all():
                new_field = FormField.objects.create(
                    form=new_form,
                    field_type=field.field_type,
                    label=field.label,
                    name=field.name,
                    placeholder=field.placeholder,
                    help_text=field.help_text,
                    required=field.required,
                    properties=field.properties,
                    order=field.order,
                    width=field.width,
                    is_visible=field.is_visible,
                    conditional_logic=field.conditional_logic
                )

                # Duplicate field options
                for option in field.options.all():
                    FormFieldOption.objects.create(
                        form_field=new_field,
                        label=option.label,
                        value=option.value,
                        order=option.order,
                        is_default=option.is_default,
                        is_enabled=option.is_enabled,
                        properties=option.properties
                    )

            # Create initial version for duplicated form
            FormVersion.create_version(
                form=new_form,
                change_type=FormVersion.ChangeType.CREATED,
                change_summary=f"Duplicated from: {original_form.name}",
                user=request.user
            )

        serializer = FormSerializer(new_form)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=['post'])
    def publish(self, request, slug=None):
        """Publish a form."""
        form = self.get_object()

        if form.status == Form.Status.PUBLISHED:
            return Response(
                {'detail': 'Form is already published.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        form.status = Form.Status.PUBLISHED
        form.save(update_fields=['status'])

        # Create version for publishing
        FormVersion.create_version(
            form=form,
            change_type=FormVersion.ChangeType.PUBLISHED,
            change_summary="Form published",
            user=request.user
        )

        serializer = self.get_serializer(form)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def archive(self, request, slug=None):
        """Archive a form."""
        form = self.get_object()

        if form.status == Form.Status.ARCHIVED:
            return Response(
                {'detail': 'Form is already archived.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        form.status = Form.Status.ARCHIVED
        form.save(update_fields=['status'])

        # Create version for archiving
        FormVersion.create_version(
            form=form,
            change_type=FormVersion.ChangeType.ARCHIVED,
            change_summary="Form archived",
            user=request.user
        )

        serializer = self.get_serializer(form)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def submissions(self, request, slug=None):
        """Get form submissions."""
        form = self.get_object()
        submissions = form.submissions.all().order_by('-created_at')

        # Apply pagination
        page = self.paginate_queryset(submissions)
        if page is not None:
            serializer = FormSubmissionSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = FormSubmissionSerializer(submissions, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def versions(self, request, slug=None):
        """Get form versions."""
        form = self.get_object()
        versions = form.versions.all().order_by('-version_number')

        serializer = FormVersionSerializer(versions, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def share(self, request, slug=None):
        """Share a form with another user."""
        form = self.get_object()

        # Get user to share with
        username = request.data.get('username')
        permission = request.data.get('permission', FormShare.Permission.VIEW)
        expires_at = request.data.get('expires_at')

        if not username:
            return Response(
                {'detail': 'Username is required.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            user_to_share_with = User.objects.get(username=username)
        except User.DoesNotExist:
            return Response(
                {'detail': 'User not found.'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Create or update share
        share, created = FormShare.objects.get_or_create(
            form=form,
            shared_with=user_to_share_with,
            defaults={
                'permission': permission,
                'shared_by': request.user,
                'expires_at': expires_at,
            }
        )

        if not created:
            share.permission = permission
            share.expires_at = expires_at
            share.is_active = True
            share.save()

        serializer = FormShareSerializer(share)
        return Response(serializer.data, status=status.HTTP_201_CREATED if created else status.HTTP_200_OK)


class FormFieldViewSet(viewsets.ModelViewSet):
    """ViewSet for form fields."""

    queryset = FormField.objects.all()
    serializer_class = FormFieldSerializer
    permission_classes = [IsAuthenticated, CanManageForm]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['form', 'field_type', 'required']
    ordering_fields = ['order', 'created_at']
    ordering = ['order']

    def get_queryset(self):
        """Filter fields based on form access."""
        user = self.request.user
        if user.is_staff:
            return FormField.objects.all()

        # Users can only access fields from forms they own or have access to
        return FormField.objects.filter(
            models.Q(form__created_by=user) |
            models.Q(form__shares__shared_with=user, form__shares__is_active=True)
        ).distinct()

    @action(detail=False, methods=['post'])
    def reorder(self, request):
        """Reorder form fields."""
        field_orders = request.data.get('field_orders', [])

        if not field_orders:
            return Response(
                {'detail': 'field_orders is required.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        with transaction.atomic():
            for item in field_orders:
                field_id = item.get('id')
                order = item.get('order')

                if field_id and order is not None:
                    try:
                        field = FormField.objects.get(id=field_id)
                        # Check permission
                        if field.form.created_by != request.user and not request.user.is_staff:
                            continue

                        field.order = order
                        field.save(update_fields=['order'])
                    except FormField.DoesNotExist:
                        continue

        return Response({'detail': 'Fields reordered successfully.'})


class FormFieldOptionViewSet(viewsets.ModelViewSet):
    """ViewSet for form field options."""

    queryset = FormFieldOption.objects.all()
    serializer_class = FormFieldOptionSerializer
    permission_classes = [IsAuthenticated, CanManageForm]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['form_field', 'is_default', 'is_enabled']
    ordering_fields = ['order', 'created_at']
    ordering = ['order']

    def get_queryset(self):
        """Filter options based on form access."""
        user = self.request.user
        if user.is_staff:
            return FormFieldOption.objects.all()

        # Users can only access options from forms they own or have access to
        return FormFieldOption.objects.filter(
            models.Q(form_field__form__created_by=user) |
            models.Q(form_field__form__shares__shared_with=user,
                     form_field__form__shares__is_active=True)
        ).distinct()


class FormSubmissionViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for form submissions (read-only)."""

    queryset = FormSubmission.objects.all()
    serializer_class = FormSubmissionSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['form', 'status', 'submitted_by']
    ordering_fields = ['created_at', 'processed_at']
    ordering = ['-created_at']

    def get_queryset(self):
        """Filter submissions based on form access."""
        user = self.request.user
        if user.is_staff:
            return FormSubmission.objects.all()

        # Users can only access submissions from forms they own or have access to
        return FormSubmission.objects.filter(
            models.Q(form__created_by=user) |
            models.Q(form__shares__shared_with=user, form__shares__is_active=True)
        ).distinct()

    @action(detail=True, methods=['post'])
    def mark_processed(self, request, pk=None):
        """Mark submission as processed."""
        submission = self.get_object()
        notes = request.data.get('notes', '')

        submission.mark_processed(notes)

        serializer = self.get_serializer(submission)
        return Response(serializer.data)


class FormVersionViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for form versions (read-only)."""

    queryset = FormVersion.objects.all()
    serializer_class = FormVersionSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['form', 'change_type', 'is_current']
    ordering_fields = ['version_number', 'created_at']
    ordering = ['-version_number']

    def get_queryset(self):
        """Filter versions based on form access."""
        user = self.request.user
        if user.is_staff:
            return FormVersion.objects.all()

        # Users can only access versions from forms they own or have access to
        return FormVersion.objects.filter(
            models.Q(form__created_by=user) |
            models.Q(form__shares__shared_with=user, form__shares__is_active=True)
        ).distinct()

    @action(detail=True, methods=['post'])
    def restore(self, request, pk=None):
        """Restore this version as current."""
        version = self.get_object()

        # Check if user can manage the form
        if version.form.created_by != request.user and not request.user.is_staff:
            return Response(
                {'detail': 'You do not have permission to restore this version.'},
                status=status.HTTP_403_FORBIDDEN
            )

        version.restore(request.user)

        return Response({'detail': 'Version restored successfully.'})


class FormShareViewSet(viewsets.ModelViewSet):
    """ViewSet for form shares."""

    queryset = FormShare.objects.all()
    serializer_class = FormShareSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['form', 'shared_with', 'permission', 'is_active']
    ordering_fields = ['created_at', 'last_accessed']
    ordering = ['-created_at']

    def get_queryset(self):
        """Filter shares based on user permissions."""
        user = self.request.user
        if user.is_staff:
            return FormShare.objects.all()

        # Users can see shares for forms they own or shares made with them
        return FormShare.objects.filter(
            models.Q(form__created_by=user) |
            models.Q(shared_with=user)
        ).distinct()

    def perform_create(self, serializer):
        """Set the sharer when creating a share."""
        serializer.save(shared_by=self.request.user)


# Form submission endpoint
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import json

@api_view(['POST'])
@permission_classes([AllowAny])
def submit_form(request, slug):
    """Handle form submissions."""
    try:
        form = get_object_or_404(Form, slug=slug)

        # Check if form can accept submissions
        if not form.can_accept_submissions():
            return Response(
                {'error': 'This form is not accepting submissions.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get submission data
        submission_data = request.data.get('data', {})

        # Create submission
        submission = FormSubmission.objects.create(
            form=form,
            data=submission_data,
            submitted_by=request.user if request.user.is_authenticated else None,
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            referrer=request.META.get('HTTP_REFERER', '')
        )

        # Update form submission count
        form.submission_count += 1
        form.save(update_fields=['submission_count'])

        return Response({
            'success': True,
            'message': form.settings.get('success_message', 'Thank you for your submission!'),
            'submission_id': submission.id
        })

    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# Web interface views (placeholder implementations)
def form_list_view(request):
    """List all forms (web interface)."""
    return render(request, 'forms/form_list.html')


def form_create_view(request):
    """Create new form (web interface)."""
    if request.method == 'POST':
        # Handle form creation from scratch
        name = request.POST.get('name', '').strip()
        description = request.POST.get('description', '').strip()

        if not name:
            messages.error(request, 'Form name is required.')
            return render(request, 'forms/form_create.html')

        try:
            # Generate unique slug
            base_slug = slugify(name)
            slug = base_slug
            counter = 1
            while Form.objects.filter(slug=slug).exists():
                slug = f"{base_slug}-{counter}"
                counter += 1

            # Get additional form options
            submission_handling = request.POST.get('submission_handling', 'database')
            max_submissions = request.POST.get('max_submissions')
            is_public = request.POST.get('is_public') == 'on'
            requires_authentication = request.POST.get('requires_authentication') == 'on'

            # Validate max_submissions
            if max_submissions:
                try:
                    max_submissions = int(max_submissions)
                    if max_submissions < 1 or max_submissions > 10000:
                        max_submissions = None
                except ValueError:
                    max_submissions = None
            else:
                max_submissions = None

            # Create the form
            form = Form.objects.create(
                name=name,
                description=description,
                slug=slug,
                created_by=request.user,
                status=Form.Status.DRAFT,
                submission_handling=submission_handling,
                max_submissions=max_submissions,
                is_public=is_public,
                requires_authentication=requires_authentication,
                settings=Form().get_default_settings()
            )

            # Create initial version
            FormVersion.create_version(
                form=form,
                change_type=FormVersion.ChangeType.CREATED,
                change_summary="Initial form creation",
                user=request.user
            )

            messages.success(request, f'Form "{name}" created successfully!')
            return redirect('forms:form_builder', slug=form.slug)

        except Exception as e:
            messages.error(request, f'Error creating form: {str(e)}')
            return render(request, 'forms/form_create.html')

    return render(request, 'forms/form_create.html')


def form_detail_view(request, slug):
    """Form detail view (web interface)."""
    form = get_object_or_404(Form, slug=slug)
    return render(request, 'forms/form_detail.html', {'form': form})


def form_edit_view(request, slug):
    """Edit form (web interface)."""
    form = get_object_or_404(Form, slug=slug)
    return render(request, 'forms/form_edit.html', {'form': form})


def form_builder_view(request, slug):
    """Form builder interface (web interface)."""
    form = get_object_or_404(Form, slug=slug)

    # Get form fields
    fields = form.fields.all().order_by('order')

    # Serialize fields for JavaScript
    form_fields_data = []
    for field in fields:
        field_data = {
            'id': field.id,
            'name': field.name,
            'label': field.label,
            'field_type': field.field_type,
            'required': field.required,
            'placeholder': field.placeholder,
            'help_text': field.help_text,
            'default_value': field.default_value,
            'order': field.order,
            'is_visible': field.is_visible,
            'properties': field.properties or {},
            'validation_rules': field.validation_rules or {},
            'conditional_logic': field.conditional_logic or {},
            'options': []
        }

        # Add field options if they exist
        if hasattr(field, 'options'):
            field_data['options'] = [
                {'value': opt.value, 'label': opt.label, 'order': opt.order}
                for opt in field.options.all().order_by('order')
            ]

        form_fields_data.append(field_data)

    # Form settings
    form_settings = {
        'submit_button_text': getattr(form, 'submit_button_text', 'Submit'),
        'success_message': getattr(form, 'success_message', 'Thank you for your submission!'),
        'redirect_url': getattr(form, 'redirect_url', ''),
        'is_public': getattr(form, 'is_public', True),
        'requires_authentication': getattr(form, 'requires_authentication', False),
        'max_submissions': getattr(form, 'max_submissions', None),
        'send_confirmation_email': getattr(form, 'send_confirmation_email', False),
        'notification_email': getattr(form, 'notification_email', ''),
        'allow_multiple_submissions': getattr(form, 'allow_multiple_submissions', True),
        'save_progress': getattr(form, 'save_progress', False),
        'show_progress_bar': getattr(form, 'show_progress_bar', True),
    }

    # Form customization (theme, colors, etc.)
    form_customization = getattr(form, 'customization', {}) or {
        'theme': 'default',
        'colors': {
            'primary': '#3B82F6',
            'secondary': '#6B7280',
            'background': '#FFFFFF',
            'text': '#1F2937',
            'border': '#D1D5DB',
            'error': '#EF4444',
            'success': '#10B981'
        },
        'typography': {
            'fontFamily': 'system',
            'fontSize': 16,
            'lineHeight': 1.5,
            'fontWeight': 'normal'
        },
        'spacing': {
            'fieldSpacing': 16,
            'formPadding': 24,
            'sectionSpacing': 32,
            'borderRadius': 6
        },
        'layout': {
            'maxWidth': '600px',
            'columns': 1,
            'fieldWidth': 'full'
        },
        'animations': {
            'enabled': True,
            'duration': 200,
            'easing': 'ease-in-out'
        },
        'customCSS': ''
    }

    context = {
        'form': form,
        'form_fields_json': json.dumps(form_fields_data),
        'form_settings_json': json.dumps(form_settings),
        'form_customization_json': json.dumps(form_customization),
    }

    return render(request, 'forms/form_builder.html', context)


def form_builder_debug_view(request, slug):
    """Form builder debug interface (web interface)."""
    form = get_object_or_404(Form, slug=slug)

    # Get form fields
    fields = form.fields.all().order_by('order')

    # Serialize fields for JavaScript
    form_fields_data = []
    for field in fields:
        field_data = {
            'id': field.id,
            'name': field.name,
            'label': field.label,
            'field_type': field.field_type,
            'required': field.required,
            'placeholder': field.placeholder,
            'help_text': field.help_text,
            'default_value': field.default_value,
            'order': field.order,
            'is_visible': field.is_visible,
            'properties': field.properties or {},
            'validation_rules': field.validation_rules or {},
            'conditional_logic': field.conditional_logic or {},
            'options': []
        }

        # Add field options if they exist
        if hasattr(field, 'options'):
            field_data['options'] = [
                {'value': opt.value, 'label': opt.label, 'order': opt.order}
                for opt in field.options.all().order_by('order')
            ]

        form_fields_data.append(field_data)

    # Form settings
    form_settings = getattr(form, 'settings', {}) or form.get_default_settings()

    # Form customization (theme, colors, etc.)
    form_customization = getattr(form, 'customization', {}) or {
        'theme': 'default',
        'colors': {
            'primary': '#3B82F6',
            'secondary': '#6B7280',
            'background': '#FFFFFF',
            'text': '#1F2937',
            'border': '#D1D5DB',
            'error': '#EF4444',
            'success': '#10B981'
        }
    }

    context = {
        'form': form,
        'form_fields_json': json.dumps(form_fields_data),
        'form_settings_json': json.dumps(form_settings),
        'form_customization_json': json.dumps(form_customization),
    }

    return render(request, 'forms/form_builder_debug.html', context)


def vue_test_view(request):
    """Vue.js test page."""
    return render(request, 'forms/vue_test.html')
