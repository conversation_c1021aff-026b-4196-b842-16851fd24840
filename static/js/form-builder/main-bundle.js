// Form Builder Main Bundle - Non-module version
(function() {
  'use strict';
  
  // Check if Vue is available
  if (typeof Vue === 'undefined') {
    console.error('Vue.js is required for the form builder');
    // Show user-friendly error message
    const appElement = document.getElementById('form-builder-app');
    if (appElement) {
      appElement.innerHTML = `
        <div style="padding: 2rem; text-align: center; color: #dc2626;">
          <h2>Form Builder Error</h2>
          <p>Vue.js is required but not loaded. Please check your internet connection and refresh the page.</p>
          <button onclick="window.location.reload()" style="padding: 0.5rem 1rem; background: #3b82f6; color: white; border: none; border-radius: 0.25rem; cursor: pointer;">
            Refresh Page
          </button>
        </div>
      `;
    }
    return;
  }
  
  const { createApp, ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } = Vue;
  
  // Form Builder Store (simplified version)
  function createFormBuilderStore() {
    const state = reactive({
      form: {
        id: null,
        name: '',
        description: '',
        fields: [],
        settings: {},
        customization: {}
      },
      selectedField: null,
      isDragging: false,
      previewMode: false,
      history: [],
      historyIndex: -1
    });
    
    const actions = {
      setForm(formData) {
        Object.assign(state.form, formData);
      },
      
      addField(fieldType, position = -1) {
        const newField = {
          id: `field-${Date.now()}`,
          type: fieldType,
          label: `${fieldType.charAt(0).toUpperCase() + fieldType.slice(1)} Field`,
          name: `field_${Date.now()}`,
          required: false,
          placeholder: '',
          helpText: '',
          validation: {},
          options: fieldType === 'select' || fieldType === 'radio' || fieldType === 'checkbox' ? [
            { label: 'Option 1', value: 'option1' },
            { label: 'Option 2', value: 'option2' }
          ] : [],
          order: position >= 0 ? position : state.form.fields.length
        };
        
        if (position >= 0) {
          state.form.fields.splice(position, 0, newField);
        } else {
          state.form.fields.push(newField);
        }
        
        this.saveToHistory();
        return newField;
      },
      
      updateField(fieldId, updates) {
        const fieldIndex = state.form.fields.findIndex(f => f.id === fieldId);
        if (fieldIndex > -1) {
          Object.assign(state.form.fields[fieldIndex], updates);
          this.saveToHistory();
        }
      },
      
      deleteField(fieldId) {
        const fieldIndex = state.form.fields.findIndex(f => f.id === fieldId);
        if (fieldIndex > -1) {
          state.form.fields.splice(fieldIndex, 1);
          if (state.selectedField && state.selectedField.id === fieldId) {
            state.selectedField = null;
          }
          this.saveToHistory();
        }
      },
      
      selectField(field) {
        state.selectedField = field;
      },
      
      togglePreview() {
        state.previewMode = !state.previewMode;
      },
      
      saveToHistory() {
        const snapshot = JSON.parse(JSON.stringify(state.form));
        state.history = state.history.slice(0, state.historyIndex + 1);
        state.history.push(snapshot);
        state.historyIndex = state.history.length - 1;
        
        // Limit history size
        if (state.history.length > 50) {
          state.history.shift();
          state.historyIndex--;
        }
      },
      
      undo() {
        if (state.historyIndex > 0) {
          state.historyIndex--;
          const snapshot = state.history[state.historyIndex];
          Object.assign(state.form, JSON.parse(JSON.stringify(snapshot)));
        }
      },
      
      redo() {
        if (state.historyIndex < state.history.length - 1) {
          state.historyIndex++;
          const snapshot = state.history[state.historyIndex];
          Object.assign(state.form, JSON.parse(JSON.stringify(snapshot)));
        }
      }
    };
    
    return { state, actions };
  }
  
  // Main Form Builder Component
  const FormBuilderMain = {
    template: `
      <div class="form-builder-main">
        <!-- Header -->
        <header class="builder-header">
          <div class="header-left">
            <h1 class="form-title">{{ form.name || 'Untitled Form' }}</h1>
            <div class="form-status">
              <span class="status-indicator" :class="form.status"></span>
              {{ form.status || 'Draft' }}
            </div>
          </div>
          <div class="header-right">
            <button @click="undo" :disabled="!canUndo" class="btn btn-icon" title="Undo">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
              </svg>
            </button>
            <button @click="redo" :disabled="!canRedo" class="btn btn-icon" title="Redo">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 10h-10a8 8 0 00-8 8v2m18-10l-6 6m6-6l-6-6"></path>
              </svg>
            </button>
            <button @click="togglePreview" class="btn btn-primary">
              {{ previewMode ? 'Edit' : 'Preview' }}
            </button>
            <button @click="saveForm" class="btn btn-success">Save</button>
          </div>
        </header>
        
        <!-- Main Content -->
        <div class="builder-content">
          <!-- Field Palette -->
          <div v-if="!previewMode" class="field-palette">
            <h3 class="palette-title">Field Types</h3>
            <div class="field-types">
              <div 
                v-for="fieldType in fieldTypes"
                :key="fieldType.type"
                @click="addField(fieldType.type)"
                class="field-type-item"
                :title="fieldType.description"
              >
                <div class="field-icon">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="fieldType.icon"></path>
                  </svg>
                </div>
                <span class="field-label">{{ fieldType.label }}</span>
              </div>
            </div>
          </div>
          
          <!-- Form Canvas -->
          <div class="form-canvas">
            <div v-if="previewMode" class="form-preview">
              <form @submit.prevent="handlePreviewSubmit" class="preview-form">
                <h2 class="form-title">{{ form.name }}</h2>
                <p v-if="form.description" class="form-description">{{ form.description }}</p>
                
                <div v-for="field in form.fields" :key="field.id" class="form-field">
                  <label :for="field.id" class="field-label">
                    {{ field.label }}
                    <span v-if="field.required" class="required">*</span>
                  </label>
                  
                  <!-- Text Input -->
                  <input 
                    v-if="field.type === 'text' || field.type === 'email'"
                    :id="field.id"
                    :type="field.type"
                    :name="field.name"
                    :placeholder="field.placeholder"
                    :required="field.required"
                    class="field-input"
                  />
                  
                  <!-- Textarea -->
                  <textarea 
                    v-else-if="field.type === 'textarea'"
                    :id="field.id"
                    :name="field.name"
                    :placeholder="field.placeholder"
                    :required="field.required"
                    class="field-textarea"
                    rows="4"
                  ></textarea>
                  
                  <!-- Select -->
                  <select 
                    v-else-if="field.type === 'select'"
                    :id="field.id"
                    :name="field.name"
                    :required="field.required"
                    class="field-select"
                  >
                    <option value="">Please select...</option>
                    <option 
                      v-for="option in field.options"
                      :key="option.value"
                      :value="option.value"
                    >
                      {{ option.label }}
                    </option>
                  </select>
                  
                  <!-- Radio Buttons -->
                  <div v-else-if="field.type === 'radio'" class="radio-group">
                    <label 
                      v-for="option in field.options"
                      :key="option.value"
                      class="radio-label"
                    >
                      <input 
                        :name="field.name"
                        type="radio"
                        :value="option.value"
                        :required="field.required"
                        class="radio-input"
                      />
                      {{ option.label }}
                    </label>
                  </div>
                  
                  <!-- Checkboxes -->
                  <div v-else-if="field.type === 'checkbox'" class="checkbox-group">
                    <label 
                      v-for="option in field.options"
                      :key="option.value"
                      class="checkbox-label"
                    >
                      <input 
                        :name="field.name + '[]'"
                        type="checkbox"
                        :value="option.value"
                        class="checkbox-input"
                      />
                      {{ option.label }}
                    </label>
                  </div>
                  
                  <p v-if="field.helpText" class="field-help">{{ field.helpText }}</p>
                </div>
                
                <button type="submit" class="submit-btn">Submit</button>
              </form>
            </div>
            
            <div v-else class="form-builder">
              <div v-if="form.fields.length === 0" class="empty-state">
                <svg class="empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                <h3>Start building your form</h3>
                <p>Click on a field type from the left panel to add it to your form</p>
              </div>
              
              <div v-else class="form-fields">
                <div 
                  v-for="(field, index) in form.fields"
                  :key="field.id"
                  @click="selectField(field)"
                  class="field-item"
                  :class="{ 'selected': selectedField && selectedField.id === field.id }"
                >
                  <div class="field-header">
                    <span class="field-type">{{ field.type }}</span>
                    <div class="field-actions">
                      <button @click.stop="moveFieldUp(index)" :disabled="index === 0" class="btn-icon">↑</button>
                      <button @click.stop="moveFieldDown(index)" :disabled="index === form.fields.length - 1" class="btn-icon">↓</button>
                      <button @click.stop="deleteField(field.id)" class="btn-icon btn-danger">×</button>
                    </div>
                  </div>
                  <div class="field-content">
                    <strong>{{ field.label }}</strong>
                    <p v-if="field.helpText" class="field-help">{{ field.helpText }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Properties Panel -->
          <div v-if="!previewMode && selectedField" class="properties-panel">
            <h3 class="panel-title">Field Properties</h3>
            <div class="property-group">
              <label class="property-label">Label</label>
              <input 
                v-model="selectedField.label"
                @input="updateSelectedField"
                type="text"
                class="property-input"
              />
            </div>
            <div class="property-group">
              <label class="property-label">Name</label>
              <input 
                v-model="selectedField.name"
                @input="updateSelectedField"
                type="text"
                class="property-input"
              />
            </div>
            <div class="property-group">
              <label class="property-label">Placeholder</label>
              <input 
                v-model="selectedField.placeholder"
                @input="updateSelectedField"
                type="text"
                class="property-input"
              />
            </div>
            <div class="property-group">
              <label class="property-label">Help Text</label>
              <textarea 
                v-model="selectedField.helpText"
                @input="updateSelectedField"
                class="property-textarea"
                rows="2"
              ></textarea>
            </div>
            <div class="property-group">
              <label class="checkbox-label">
                <input 
                  v-model="selectedField.required"
                  @change="updateSelectedField"
                  type="checkbox"
                  class="property-checkbox"
                />
                Required field
              </label>
            </div>
            
            <!-- Options for select, radio, checkbox -->
            <div v-if="['select', 'radio', 'checkbox'].includes(selectedField.type)" class="property-group">
              <label class="property-label">Options</label>
              <div class="options-list">
                <div 
                  v-for="(option, index) in selectedField.options"
                  :key="index"
                  class="option-item"
                >
                  <input 
                    v-model="option.label"
                    @input="updateSelectedField"
                    type="text"
                    class="option-input"
                    placeholder="Option label"
                  />
                  <input 
                    v-model="option.value"
                    @input="updateSelectedField"
                    type="text"
                    class="option-input"
                    placeholder="Option value"
                  />
                  <button @click="removeOption(index)" class="btn-icon btn-danger">×</button>
                </div>
                <button @click="addOption" class="btn btn-sm">Add Option</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    `,
    
    setup() {
      const store = createFormBuilderStore();
      const { state, actions } = store;
      
      const fieldTypes = [
        { type: 'text', label: 'Text', description: 'Single line text input', icon: 'M4 6h16M4 12h16M4 18h7' },
        { type: 'textarea', label: 'Textarea', description: 'Multi-line text input', icon: 'M4 6h16M4 10h16M4 14h16M4 18h10' },
        { type: 'email', label: 'Email', description: 'Email address input', icon: 'M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z' },
        { type: 'select', label: 'Select', description: 'Dropdown selection', icon: 'M8 9l4-4 4 4m0 6l-4 4-4-4' },
        { type: 'radio', label: 'Radio', description: 'Single choice from options', icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' },
        { type: 'checkbox', label: 'Checkbox', description: 'Multiple choice options', icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' }
      ];
      
      const canUndo = computed(() => state.historyIndex > 0);
      const canRedo = computed(() => state.historyIndex < state.history.length - 1);
      
      const addField = (fieldType) => {
        actions.addField(fieldType);
      };
      
      const selectField = (field) => {
        actions.selectField(field);
      };
      
      const updateSelectedField = () => {
        if (state.selectedField) {
          actions.updateField(state.selectedField.id, state.selectedField);
        }
      };
      
      const moveFieldUp = (index) => {
        if (index > 0) {
          const field = state.form.fields.splice(index, 1)[0];
          state.form.fields.splice(index - 1, 0, field);
          actions.saveToHistory();
        }
      };
      
      const moveFieldDown = (index) => {
        if (index < state.form.fields.length - 1) {
          const field = state.form.fields.splice(index, 1)[0];
          state.form.fields.splice(index + 1, 0, field);
          actions.saveToHistory();
        }
      };
      
      const addOption = () => {
        if (state.selectedField && state.selectedField.options) {
          state.selectedField.options.push({
            label: `Option ${state.selectedField.options.length + 1}`,
            value: `option${state.selectedField.options.length + 1}`
          });
          updateSelectedField();
        }
      };
      
      const removeOption = (index) => {
        if (state.selectedField && state.selectedField.options) {
          state.selectedField.options.splice(index, 1);
          updateSelectedField();
        }
      };
      
      const saveForm = async () => {
        try {
          const response = await fetch(`/forms/${state.form.id}/save/`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRFToken': window.formBuilderData.csrfToken
            },
            body: JSON.stringify({
              name: state.form.name,
              description: state.form.description,
              fields: state.form.fields,
              settings: state.form.settings,
              customization: state.form.customization
            })
          });
          
          if (response.ok) {
            console.log('Form saved successfully');
          } else {
            console.error('Failed to save form');
          }
        } catch (error) {
          console.error('Error saving form:', error);
        }
      };
      
      const handlePreviewSubmit = () => {
        alert('This is a preview. Form submission is not functional.');
      };
      
      // Initialize with form data
      onMounted(() => {
        if (window.formBuilderData && window.formBuilderData.formData) {
          actions.setForm(window.formBuilderData.formData);
          actions.saveToHistory();
        }
      });
      
      return {
        // State
        form: state.form,
        selectedField: state.selectedField,
        previewMode: state.previewMode,
        
        // Computed
        canUndo,
        canRedo,
        
        // Data
        fieldTypes,
        
        // Methods
        addField,
        selectField,
        updateSelectedField,
        moveFieldUp,
        moveFieldDown,
        addOption,
        removeOption,
        saveForm,
        handlePreviewSubmit,
        undo: actions.undo,
        redo: actions.redo,
        deleteField: actions.deleteField,
        togglePreview: actions.togglePreview
      };
    }
  };
  
  // Initialize the application
  function initFormBuilder() {
    try {
      // Check if the mount element exists
      const mountElement = document.getElementById('form-builder-app');
      if (!mountElement) {
        console.error('Form Builder mount element #form-builder-app not found');
        return null;
      }

      const app = createApp(FormBuilderMain);

      // Global error handler
      app.config.errorHandler = (err, instance, info) => {
        console.error('Form Builder Error:', err, info);

        // Show user-friendly error message
        mountElement.innerHTML = `
          <div style="padding: 2rem; text-align: center; color: #dc2626; background: #fef2f2; border: 1px solid #fecaca; border-radius: 0.5rem; margin: 1rem;">
            <h3>Something went wrong</h3>
            <p>The form builder encountered an error: ${err.message}</p>
            <details style="margin-top: 1rem; text-align: left;">
              <summary style="cursor: pointer;">Technical Details</summary>
              <pre style="background: #f3f4f6; padding: 1rem; border-radius: 0.25rem; margin-top: 0.5rem; overflow-x: auto;">${err.stack}</pre>
            </details>
            <button onclick="window.location.reload()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: #3b82f6; color: white; border: none; border-radius: 0.25rem; cursor: pointer;">
              Refresh Page
            </button>
          </div>
        `;
      };

      // Mount the app
      app.mount('#form-builder-app');

      console.log('Form Builder initialized successfully');
      return app;

    } catch (error) {
      console.error('Failed to initialize Form Builder:', error);

      const mountElement = document.getElementById('form-builder-app');
      if (mountElement) {
        mountElement.innerHTML = `
          <div style="padding: 2rem; text-align: center; color: #dc2626; background: #fef2f2; border: 1px solid #fecaca; border-radius: 0.5rem; margin: 1rem;">
            <h3>Initialization Error</h3>
            <p>Failed to start the form builder: ${error.message}</p>
            <button onclick="window.location.reload()" style="margin-top: 1rem; padding: 0.5rem 1rem; background: #3b82f6; color: white; border: none; border-radius: 0.25rem; cursor: pointer;">
              Refresh Page
            </button>
          </div>
        `;
      }

      return null;
    }
  }
  
  // Debug information
  console.log('Form Builder Script Loaded');
  console.log('Vue available:', typeof Vue !== 'undefined');
  console.log('Form data available:', typeof window.formBuilderData !== 'undefined');

  // Auto-initialize when DOM is ready
  if (document.readyState === 'loading') {
    console.log('DOM still loading, waiting for DOMContentLoaded');
    document.addEventListener('DOMContentLoaded', () => {
      console.log('DOMContentLoaded fired, initializing Form Builder');
      initFormBuilder();
    });
  } else {
    console.log('DOM already loaded, initializing Form Builder immediately');
    initFormBuilder();
  }
  
  // Export for manual initialization
  window.initFormBuilder = initFormBuilder;
  
})();
