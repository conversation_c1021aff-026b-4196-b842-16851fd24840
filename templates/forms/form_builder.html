{% extends 'base.html' %}
{% load static %}
{% load vite_tags %}

{% block title %}
  Form Builder - PDFlex
{% endblock %}

{% block content %}
  <!-- Form Builder Vue App Container -->
  <div id="form-builder-app" class="min-h-screen">
    <!-- Loading state while Vue app initializes -->
    <div id="loading-state" class="flex items-center justify-center min-h-screen bg-gray-50">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p class="text-gray-600 text-lg font-medium">Loading Form Builder...</p>
        <p class="text-sm text-gray-500 mt-2">Initializing Vue.js interface...</p>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <!-- Form Builder Data -->
  <script>
    console.log('Setting up comprehensive form builder data...')
    
    try {
      // Parse JSON data safely
      const fieldsData = JSON.parse('{{ form_fields_json|escapejs }}')
      const settingsData = JSON.parse('{{ form_settings_json|escapejs }}')
      const customizationData = JSON.parse('{{ form_customization_json|escapejs }}')
    
      // Comprehensive form data setup with all features
      window.formBuilderData = {
        formData: {
          id: '{{ form.id }}',
          name: '{{ form.name|escapejs }}',
          description: '{{ form.description|escapejs }}',
          status: '{{ form.status|escapejs }}',
          fields: fieldsData,
          settings: settingsData,
          customization: customizationData
        },
        csrfToken: '{{ csrf_token }}',
        apiBaseUrl: '/api/v1',
        formSlug: '{{ form.slug }}'
      }
    
      console.log('Comprehensive Form Builder Data:', window.formBuilderData)
    } catch (error) {
      console.error('Error parsing form builder data:', error)
      // Fallback data
      window.formBuilderData = {
        formData: {
          id: '{{ form.id }}',
          name: '{{ form.name|escapejs }}',
          description: '{{ form.description|escapejs }}',
          status: '{{ form.status|escapejs }}',
          fields: [],
          settings: {},
          customization: {}
        },
        csrfToken: '{{ csrf_token }}',
        apiBaseUrl: '/api/v1',
        formSlug: '{{ form.slug }}'
      }
    }
  </script>

  <!-- Load Vue.js and Form Builder Assets -->
  {% load_vite_assets 'form-builder' %}

  <!-- Comprehensive Form Builder Initialization -->
  <script>
    // Initialize the form builder application
    document.addEventListener('DOMContentLoaded', () => {
      console.log('DOM Content Loaded - Initializing Comprehensive Form Builder')

      const formBuilderElement = document.getElementById('form-builder-app')
      console.log('Form Builder Element:', formBuilderElement)
      console.log('Form Builder Data:', window.formBuilderData)

      if (!formBuilderElement) {
        console.error('Form builder element not found!')
        return
      }

      if (!window.formBuilderData) {
        console.error('Form builder data not found!')
        return
      }

      try {
        console.log('Initializing comprehensive Vue app with all features...')

        // Check if the initFormBuilder function is available (from Vite build)
        if (typeof window.initFormBuilder === 'function') {
          console.log('Using Vite-built form builder')
          const app = window.initFormBuilder(
            '#form-builder-app',
            window.formBuilderData.formData,
            window.formBuilderData.csrfToken
          )
          console.log('Comprehensive Vue app initialized successfully:', app)
        } else {
          console.log('Vite build not available, using fallback implementation')
          initFallbackFormBuilder()
        }
      } catch (error) {
        console.error('Error initializing comprehensive form builder:', error)
        initFallbackFormBuilder()
      }
    })

    // Fallback form builder implementation
    function initFallbackFormBuilder() {
      console.log('Initializing fallback form builder...')

      // Hide loading state
      const loadingState = document.getElementById('loading-state')
      if (loadingState) {
        loadingState.style.display = 'none'
      }

      // Check if Vue is available from CDN
      if (typeof Vue !== 'undefined') {
        const { createApp } = Vue

        const app = createApp({
          data() {
            return {
              formData: window.formBuilderData?.formData || {},
              csrfToken: window.formBuilderData?.csrfToken || '',
              activeTab: 'fields',
              selectedField: null,
              draggedField: null,
              formFields: window.formBuilderData?.formData?.fields || [],
              availableFieldTypes: [
                { id: 'text', name: 'Text Input', icon: 'T', color: 'blue', description: 'Single line text field' },
                { id: 'email', name: 'Email', icon: '@', color: 'green', description: 'Email address field' },
                { id: 'textarea', name: 'Textarea', icon: '¶', color: 'indigo', description: 'Multi-line text field' },
                { id: 'select', name: 'Select', icon: '▼', color: 'purple', description: 'Dropdown selection' },
                { id: 'radio', name: 'Radio', icon: '◉', color: 'pink', description: 'Single choice' },
                { id: 'checkbox', name: 'Checkbox', icon: '☐', color: 'orange', description: 'Multiple choice' },
                { id: 'number', name: 'Number', icon: '#', color: 'red', description: 'Numeric input' },
                { id: 'date', name: 'Date', icon: '📅', color: 'yellow', description: 'Date picker' },
                { id: 'file', name: 'File Upload', icon: '📎', color: 'gray', description: 'File attachment' }
              ]
            }
          },
          mounted() {
            console.log('Fallback Vue app mounted successfully')
          },
          methods: {
            addField(fieldType) {
              const newField = {
                id: 'field_' + Date.now(),
                type: fieldType.id,
                name: fieldType.name.toLowerCase().replace(/\s+/g, '_'),
                label: fieldType.name,
                required: false,
                placeholder: '',
                help_text: '',
                order: this.formFields.length,
                properties: {},
                validation_rules: {},
                options: fieldType.id === 'select' || fieldType.id === 'radio' || fieldType.id === 'checkbox' ? [
                  { value: 'option1', label: 'Option 1' },
                  { value: 'option2', label: 'Option 2' }
                ] : []
              }
              this.formFields.push(newField)
              console.log('Added field:', newField)
            },
            removeField(fieldId) {
              this.formFields = this.formFields.filter(field => field.id !== fieldId)
              if (this.selectedField && this.selectedField.id === fieldId) {
                this.selectedField = null
              }
            },
            selectField(field) {
              this.selectedField = field
            },
            saveForm() {
              console.log('Saving form with fields:', this.formFields)
              // TODO: Implement save functionality
              alert('Form save functionality will be implemented')
            },
            previewForm() {
              console.log('Previewing form')
              // TODO: Implement preview functionality
              alert('Form preview functionality will be implemented')
            }
          },
            template: `
                      <div class="min-h-screen bg-gray-50">
                        <!-- Header -->
                        <div class="bg-white border-b border-gray-200 px-6 py-4">
                          <div class="flex items-center justify-between">
                            <div>
                              <h1 class="text-2xl font-bold text-gray-900">{{ formData.name }}</h1>
                              <p class="text-sm text-gray-500">Form Builder • {{ formData.status }} • {{ formFields.length }} fields</p>
                            </div>
                            <div class="flex space-x-3">
                              <button @click="previewForm" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                Preview
                              </button>
                              <button @click="saveForm" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700">
                                Save Form
                              </button>
                            </div>
                          </div>
                        </div>
        
                        <!-- Main Content -->
                        <div class="flex h-screen">
                          <!-- Sidebar -->
                          <div class="w-80 bg-white border-r border-gray-200 overflow-y-auto">
                            <div class="p-6">
                              <h2 class="text-lg font-semibold text-gray-900 mb-4">Form Elements</h2>
        
                              <!-- Sidebar Tabs -->
                              <div class="flex border-b border-gray-200 mb-4">
                                <button @click="activeTab = 'fields'" :class="{'border-blue-500 text-blue-600': activeTab === 'fields', 'border-transparent text-gray-500': activeTab !== 'fields'}" class="px-4 py-2 border-b-2 font-medium text-sm">
                                  Fields
                                </button>
                                <button @click="activeTab = 'design'" :class="{'border-blue-500 text-blue-600': activeTab === 'design', 'border-transparent text-gray-500': activeTab !== 'design'}" class="px-4 py-2 border-b-2 font-medium text-sm">
                                  Design
                                </button>
                                <button @click="activeTab = 'settings'" :class="{'border-blue-500 text-blue-600': activeTab === 'settings', 'border-transparent text-gray-500': activeTab !== 'settings'}" class="px-4 py-2 border-b-2 font-medium text-sm">
                                  Settings
                                </button>
                              </div>

                              <!-- Fields Tab -->
                              <div v-if="activeTab === 'fields'">
                                <h3 class="text-sm font-medium text-gray-900 mb-3">Field Types</h3>
                                <div class="space-y-2">
                                  <div v-for="fieldType in availableFieldTypes" :key="fieldType.id"
                                       @click="addField(fieldType)"
                                       class="p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 hover:border-gray-300 transition-colors">
                                    <div class="flex items-center">
                                      <div :class="'w-8 h-8 bg-' + fieldType.color + '-100 rounded flex items-center justify-center mr-3'">
                                        <span :class="'text-' + fieldType.color + '-600 text-sm font-medium'">{{ fieldType.icon }}</span>
                                      </div>
                                      <div>
                                        <p class="font-medium text-gray-900">{{ fieldType.name }}</p>
                                        <p class="text-xs text-gray-500">{{ fieldType.description }}</p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <!-- Design Tab -->
                              <div v-if="activeTab === 'design'">
                                <h3 class="text-sm font-medium text-gray-900 mb-3">Form Styling</h3>
                                <div class="space-y-4">
                                  <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Theme</label>
                                    <select v-model="formData.customization.theme" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                      <option value="default">Default</option>
                                      <option value="modern">Modern</option>
                                      <option value="minimal">Minimal</option>
                                      <option value="classic">Classic</option>
                                    </select>
                                  </div>
                                  <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Primary Color</label>
                                    <input v-model="formData.customization.colors.primary" type="color" class="w-full h-10 border border-gray-300 rounded-md">
                                  </div>
                                </div>
                              </div>

                              <!-- Settings Tab -->
                              <div v-if="activeTab === 'settings'">
                                <h3 class="text-sm font-medium text-gray-900 mb-3">Form Settings</h3>
                                <div class="space-y-4">
                                  <div>
                                    <label class="flex items-center">
                                      <input v-model="formData.settings.allowMultipleSubmissions" type="checkbox" class="rounded border-gray-300 text-blue-600 mr-2">
                                      <span class="text-sm text-gray-700">Allow multiple submissions</span>
                                    </label>
                                  </div>
                                  <div>
                                    <label class="flex items-center">
                                      <input v-model="formData.settings.requireLogin" type="checkbox" class="rounded border-gray-300 text-blue-600 mr-2">
                                      <span class="text-sm text-gray-700">Require login to submit</span>
                                    </label>
                                  </div>
                                  <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Success Message</label>
                                    <textarea v-model="formData.settings.successMessage" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" rows="3" placeholder="Thank you for your submission!"></textarea>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
        
                          <!-- Form Canvas -->
                          <div class="flex-1 flex">
                            <!-- Form Preview -->
                            <div class="flex-1 p-6">
                              <div class="max-w-2xl mx-auto">
                                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
                                  <!-- Form Header -->
                                  <div class="mb-6">
                                    <h2 class="text-xl font-bold text-gray-900">{{ formData.name }}</h2>
                                    <p v-if="formData.description" class="text-gray-600 mt-1">{{ formData.description }}</p>
                                  </div>

                                  <!-- Form Fields -->
                                  <div v-if="formFields.length > 0" class="space-y-6">
                                    <div v-for="(field, index) in formFields" :key="field.id"
                                         @click="selectField(field)"
                                         :class="{'ring-2 ring-blue-500': selectedField && selectedField.id === field.id}"
                                         class="relative p-4 border border-gray-200 rounded-lg cursor-pointer hover:border-gray-300 transition-colors">

                                      <!-- Field Label -->
                                      <label class="block text-sm font-medium text-gray-700 mb-2">
                                        {{ field.label }}
                                        <span v-if="field.required" class="text-red-500">*</span>
                                      </label>

                                      <!-- Field Input (Preview) -->
                                      <div v-if="field.type === 'text' || field.type === 'email' || field.type === 'number'">
                                        <input :type="field.type" :placeholder="field.placeholder"
                                               class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" disabled>
                                      </div>

                                      <div v-else-if="field.type === 'textarea'">
                                        <textarea :placeholder="field.placeholder" rows="3"
                                                  class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" disabled></textarea>
                                      </div>

                                      <div v-else-if="field.type === 'select'">
                                        <select class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" disabled>
                                          <option value="">Choose an option</option>
                                          <option v-for="option in field.options" :key="option.value" :value="option.value">
                                            {{ option.label }}
                                          </option>
                                        </select>
                                      </div>

                                      <div v-else-if="field.type === 'radio'">
                                        <div class="space-y-2">
                                          <label v-for="option in field.options" :key="option.value" class="flex items-center">
                                            <input type="radio" :name="field.name" :value="option.value" class="mr-2" disabled>
                                            <span class="text-sm text-gray-700">{{ option.label }}</span>
                                          </label>
                                        </div>
                                      </div>

                                      <div v-else-if="field.type === 'checkbox'">
                                        <div class="space-y-2">
                                          <label v-for="option in field.options" :key="option.value" class="flex items-center">
                                            <input type="checkbox" :value="option.value" class="mr-2" disabled>
                                            <span class="text-sm text-gray-700">{{ option.label }}</span>
                                          </label>
                                        </div>
                                      </div>

                                      <div v-else-if="field.type === 'date'">
                                        <input type="date" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" disabled>
                                      </div>

                                      <div v-else-if="field.type === 'file'">
                                        <input type="file" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" disabled>
                                      </div>

                                      <!-- Help Text -->
                                      <p v-if="field.help_text" class="text-xs text-gray-500 mt-1">{{ field.help_text }}</p>

                                      <!-- Field Actions -->
                                      <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                        <button @click.stop="removeField(field.id)" class="p-1 text-red-500 hover:text-red-700">
                                          <span class="text-xs">✕</span>
                                        </button>
                                      </div>
                                    </div>
                                  </div>

                                  <!-- Empty State -->
                                  <div v-else class="text-center py-12">
                                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                      <span class="text-gray-400 text-2xl">+</span>
                                    </div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">Start Building Your Form</h3>
                                    <p class="text-gray-500">Click on field types in the sidebar to add them to your form</p>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Field Properties Panel -->
                            <div v-if="selectedField" class="w-80 bg-white border-l border-gray-200 p-6">
                              <h3 class="text-lg font-semibold text-gray-900 mb-4">Field Properties</h3>

                              <div class="space-y-4">
                                <div>
                                  <label class="block text-sm font-medium text-gray-700 mb-2">Label</label>
                                  <input v-model="selectedField.label" type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                </div>

                                <div>
                                  <label class="block text-sm font-medium text-gray-700 mb-2">Field Name</label>
                                  <input v-model="selectedField.name" type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                </div>

                                <div>
                                  <label class="block text-sm font-medium text-gray-700 mb-2">Placeholder</label>
                                  <input v-model="selectedField.placeholder" type="text" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                                </div>

                                <div>
                                  <label class="block text-sm font-medium text-gray-700 mb-2">Help Text</label>
                                  <textarea v-model="selectedField.help_text" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm" rows="2"></textarea>
                                </div>

                                <div>
                                  <label class="flex items-center">
                                    <input v-model="selectedField.required" type="checkbox" class="rounded border-gray-300 text-blue-600 mr-2">
                                    <span class="text-sm text-gray-700">Required field</span>
                                  </label>
                                </div>

                                <!-- Options for select, radio, checkbox fields -->
                                <div v-if="selectedField.type === 'select' || selectedField.type === 'radio' || selectedField.type === 'checkbox'">
                                  <label class="block text-sm font-medium text-gray-700 mb-2">Options</label>
                                  <div class="space-y-2">
                                    <div v-for="(option, index) in selectedField.options" :key="index" class="flex items-center space-x-2">
                                      <input v-model="option.label" type="text" class="flex-1 border border-gray-300 rounded-md px-3 py-2 text-sm" placeholder="Option label">
                                      <button @click="selectedField.options.splice(index, 1)" class="p-2 text-red-500 hover:text-red-700">
                                        <span class="text-xs">✕</span>
                                      </button>
                                    </div>
                                    <button @click="selectedField.options.push({value: 'option' + (selectedField.options.length + 1), label: 'Option ' + (selectedField.options.length + 1)})"
                                            class="w-full p-2 border border-dashed border-gray-300 rounded-md text-sm text-gray-500 hover:border-gray-400">
                                      + Add Option
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    `
          })
    
          console.log('Mounting Vue app...')
          app.mount('#form-builder-app')
          console.log('Vue app mounted successfully')
        } catch (error) {
          console.error('Error creating Vue app:', error)
          showFallback()
        }
      } else {
        console.error('Vue.js not available')
        showFallback()
      }
    })
    
    function showFallback() {
      console.log('Showing fallback content')
      const loadingState = document.getElementById('loading-state')
      const fallbackContent = document.getElementById('fallback-content')
    
      if (loadingState) {
        loadingState.style.display = 'none'
      }
      if (fallbackContent) {
        fallbackContent.classList.remove('hidden')
      }
    }
  </script>
{% endblock %}
