{% extends 'base.html' %}
{% load static %}
{% load vite_tags %}

{% block title %}
  Form Builder - PDFlex
{% endblock %}

{% block content %}
  <!-- Form Builder Vue App Container -->
  <div id="form-builder-app" class="min-h-screen">
    <!-- Loading state while Vue app initializes -->
    <div id="loading-state" class="flex items-center justify-center min-h-screen bg-gray-50">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p class="text-gray-600 text-lg font-medium">Loading Form Builder...</p>
        <p class="text-sm text-gray-500 mt-2">Initializing Vue.js interface...</p>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <!-- Form Builder Data -->
  <script>
    console.log('Setting up form builder data...')
    
    // Simple form data setup
    window.formBuilderData = {
      formData: {
        id: '{{ form.id }}',
        name: '{{ form.name|escapejs }}',
        description: '{{ form.description|escapejs }}',
        status: '{{ form.status|escapejs }}'
      },
      csrfToken: '{{ csrf_token }}'
    }
    
    console.log('Form Builder Data:', window.formBuilderData)
  </script>

  <!-- Load Vue.js from CDN for testing -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

  <!-- Simple Vue.js test -->
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      console.log('DOM loaded, testing Vue.js...')
      console.log('Vue available:', typeof Vue)
    
      if (typeof Vue !== 'undefined') {
        try {
          const { createApp } = Vue
    
          console.log('Creating Vue app...')
          const app = createApp({
            data() {
              return {
                message: 'Vue.js Form Builder is Working!',
                formId: '{{ form.id }}',
                formName: '{{ form.name|escapejs }}',
                formStatus: '{{ form.status|escapejs }}'
              }
            },
            mounted() {
              console.log('Vue app mounted successfully')
    
              // Hide loading state
              const loadingState = document.getElementById('loading-state')
              if (loadingState) {
                loadingState.style.display = 'none'
              }
            },
            template: `
                  <div class="min-h-screen bg-gray-50">
                    <!-- Header -->
                    <div class="bg-white border-b border-gray-200 px-6 py-4">
                      <div class="flex items-center justify-between">
                        <div>
                          <h1 class="text-2xl font-bold text-gray-900">{{ formName }}</h1>
                          <p class="text-sm text-gray-500">Form Builder • {{ formStatus }}</p>
                        </div>
                        <div class="flex space-x-3">
                          <button class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            Preview
                          </button>
                          <button class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700">
                            Save Form
                          </button>
                        </div>
                      </div>
                    </div>
    
                    <!-- Main Content -->
                    <div class="flex h-screen">
                      <!-- Sidebar -->
                      <div class="w-80 bg-white border-r border-gray-200 overflow-y-auto">
                        <div class="p-6">
                          <h2 class="text-lg font-semibold text-gray-900 mb-4">Form Elements</h2>
    
                          <!-- Field Types -->
                          <div class="space-y-2">
                            <div class="p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                              <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded flex items-center justify-center mr-3">
                                  <span class="text-blue-600 text-sm font-medium">T</span>
                                </div>
                                <div>
                                  <p class="font-medium text-gray-900">Text Input</p>
                                  <p class="text-xs text-gray-500">Single line text field</p>
                                </div>
                              </div>
                            </div>
    
                            <div class="p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                              <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-100 rounded flex items-center justify-center mr-3">
                                  <span class="text-green-600 text-sm font-medium">@</span>
                                </div>
                                <div>
                                  <p class="font-medium text-gray-900">Email</p>
                                  <p class="text-xs text-gray-500">Email address field</p>
                                </div>
                              </div>
                            </div>
    
                            <div class="p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                              <div class="flex items-center">
                                <div class="w-8 h-8 bg-purple-100 rounded flex items-center justify-center mr-3">
                                  <span class="text-purple-600 text-sm font-medium">▼</span>
                                </div>
                                <div>
                                  <p class="font-medium text-gray-900">Select</p>
                                  <p class="text-xs text-gray-500">Dropdown selection</p>
                                </div>
                              </div>
                            </div>
    
                            <div class="p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                              <div class="flex items-center">
                                <div class="w-8 h-8 bg-orange-100 rounded flex items-center justify-center mr-3">
                                  <span class="text-orange-600 text-sm font-medium">☐</span>
                                </div>
                                <div>
                                  <p class="font-medium text-gray-900">Checkbox</p>
                                  <p class="text-xs text-gray-500">Multiple choice</p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
    
                      <!-- Form Canvas -->
                      <div class="flex-1 p-6">
                        <div class="max-w-2xl mx-auto">
                          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
                            <div class="text-center py-12">
                              <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-gray-400 text-2xl">+</span>
                              </div>
                              <h3 class="text-lg font-medium text-gray-900 mb-2">Start Building Your Form</h3>
                              <p class="text-gray-500">Drag and drop elements from the sidebar to build your form</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                `
          })
    
          console.log('Mounting Vue app...')
          app.mount('#form-builder-app')
          console.log('Vue app mounted successfully')
        } catch (error) {
          console.error('Error creating Vue app:', error)
          showFallback()
        }
      } else {
        console.error('Vue.js not available')
        showFallback()
      }
    })
    
    function showFallback() {
      console.log('Showing fallback content')
      const loadingState = document.getElementById('loading-state')
      const fallbackContent = document.getElementById('fallback-content')
    
      if (loadingState) {
        loadingState.style.display = 'none'
      }
      if (fallbackContent) {
        fallbackContent.classList.remove('hidden')
      }
    }
  </script>
{% endblock %}
