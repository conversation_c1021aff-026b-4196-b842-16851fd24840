{% extends 'base.html' %}
{% load static %}
{% load vite_tags %}

{% block title %}
  Form Builder - PDFlex
{% endblock %}

{% block content %}
  <!-- Immediate HTML content (no JavaScript required) -->
  <div class="min-h-screen bg-yellow-50 p-4">
    <div class="max-w-4xl mx-auto">
      <div class="bg-yellow-100 border border-yellow-400 p-4 rounded-lg mb-4">
        <h1 class="text-xl font-bold text-yellow-800">🔧 Form Builder Diagnostic</h1>
        <p class="text-yellow-700 mt-2">This content appears immediately without JavaScript.</p>
        <p class="text-sm text-yellow-600 mt-1">
          Form: <strong>{{ form.name }}</strong> ({{ form.status }})
        </p>
      </div>
    </div>
  </div>

  <!-- Form Builder Vue App Container -->
  <div id="form-builder-app" class="min-h-screen">
    <!-- Loading state while Vue app initializes -->
    <div id="loading-state" class="flex items-center justify-center min-h-screen bg-gray-50">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p class="text-gray-600">Loading Form Builder...</p>
        <p class="text-sm text-gray-500 mt-2">If this persists, check the browser console for errors.</p>

        <!-- Immediate visual confirmation -->
        <div class="mt-6 p-4 bg-white rounded-lg shadow">
          <p class="text-green-600 font-medium">✅ Page loaded successfully!</p>
          <p class="text-sm text-gray-600 mt-1">Form: {{ form.name }} ({{ form.status }})</p>
          <p class="text-xs text-gray-500 mt-1">Waiting for Vue.js to mount...</p>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <!-- Form Builder Data -->
  <script>
    console.log('Setting up form builder data...')
    
    // Simple form data setup
    window.formBuilderData = {
      formData: {
        id: '{{ form.id }}',
        name: '{{ form.name|escapejs }}',
        description: '{{ form.description|escapejs }}',
        status: '{{ form.status|escapejs }}'
      },
      csrfToken: '{{ csrf_token }}'
    }
    
    console.log('Form Builder Data:', window.formBuilderData)
  </script>

  <!-- Load Vue.js from CDN for testing -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

  <!-- Simple Vue.js test -->
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      console.log('DOM loaded, testing Vue.js...')
      console.log('Vue available:', typeof Vue)
    
      if (typeof Vue !== 'undefined') {
        try {
          const { createApp } = Vue
    
          console.log('Creating Vue app...')
          const app = createApp({
            data() {
              return {
                message: 'Vue.js Form Builder is Working!',
                formId: '{{ form.id }}',
                formName: '{{ form.name|escapejs }}',
                formStatus: '{{ form.status|escapejs }}'
              }
            },
            mounted() {
              console.log('Vue app mounted successfully')
    
              // Hide loading state
              const loadingState = document.getElementById('loading-state')
              if (loadingState) {
                loadingState.style.display = 'none'
              }
            },
            template: `
                          <div class="min-h-screen bg-gray-50 p-8">
                            <div class="max-w-4xl mx-auto">
                              <h1 class="text-3xl font-bold text-green-600 mb-6">{{ message }}</h1>
            
                              <div class="bg-white p-6 rounded-lg shadow mb-6">
                                <h2 class="text-xl font-semibold mb-4">Form Information</h2>
                                <div class="space-y-2">
                                  <p><strong>Form ID:</strong> {{ formId }}</p>
                                  <p><strong>Form Name:</strong> {{ formName }}</p>
                                  <p><strong>Status:</strong> {{ formStatus }}</p>
                                </div>
                              </div>
            
                              <div class="bg-green-50 p-4 rounded-lg">
                                <p class="text-green-800">✅ Vue.js is working correctly!</p>
                                <p class="text-sm text-green-600 mt-1">The form builder interface can now be built on this foundation.</p>
                              </div>
                            </div>
                          </div>
                        `
          })
    
          console.log('Mounting Vue app...')
          app.mount('#form-builder-app')
          console.log('Vue app mounted successfully')
        } catch (error) {
          console.error('Error creating Vue app:', error)
          showFallback()
        }
      } else {
        console.error('Vue.js not available')
        showFallback()
      }
    })
    
    function showFallback() {
      console.log('Showing fallback content')
      const loadingState = document.getElementById('loading-state')
      const fallbackContent = document.getElementById('fallback-content')
    
      if (loadingState) {
        loadingState.style.display = 'none'
      }
      if (fallbackContent) {
        fallbackContent.classList.remove('hidden')
      }
    }
  </script>
{% endblock %}
