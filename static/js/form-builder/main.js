// Form Builder main entry point
import '../main.js' // Import base styles and setup
import { createApp } from 'vue'
import FormBuilderMain from './components/FormBuilderMain.vue'
import { createFormBuilderStore } from './stores/formBuilderStore'

// Global styles
import './styles/main.css'

// Create and mount the Vue app
window.initFormBuilder = function(elementId, formData, csrfToken) {
  console.log('initFormBuilder called with:', { elementId, formData, csrfToken })

  try {
    const app = createApp({
      components: {
        FormBuilderMain
      },
      data() {
        return {
          formData: formData || {},
          csrfToken: csrfToken || ''
        }
      },
      mounted() {
        console.log('Vue app mounted successfully')
        // Hide loading state
        const loadingState = document.getElementById('loading-state')
        if (loadingState) {
          loadingState.style.display = 'none'
        }
      },
      template: `
        <form-builder-main
          :form-data="formData"
          :csrf-token="csrfToken"
        ></form-builder-main>
      `
    })

    // Create and provide the store
    const store = createFormBuilderStore()
    app.provide('formBuilderStore', store)

    // Global error handler
    app.config.errorHandler = (err, instance, info) => {
      console.error('Form Builder Error:', err, info)
    }

    console.log('Mounting Vue app to:', elementId)
    // Mount the app
    const mountedApp = app.mount(elementId)
    console.log('Vue app mounted:', mountedApp)

    return app
  } catch (error) {
    console.error('Error in initFormBuilder:', error)
    throw error
  }
}

// Initialize the form builder application
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM Content Loaded - Initializing Form Builder')

  const formBuilderElement = document.getElementById('form-builder-app')
  console.log('Form Builder Element:', formBuilderElement)
  console.log('Form Builder Data:', window.formBuilderData)

  if (!formBuilderElement) {
    console.error('Form builder element not found!')
    return
  }

  if (!window.formBuilderData) {
    console.error('Form builder data not found!')
    return
  }

  try {
    console.log('Initializing Vue app...')
    const app = window.initFormBuilder(
      '#form-builder-app',
      window.formBuilderData.formData,
      window.formBuilderData.csrfToken
    )
    console.log('Vue app initialized successfully:', app)
  } catch (error) {
    console.error('Error initializing form builder:', error)
  }
})
