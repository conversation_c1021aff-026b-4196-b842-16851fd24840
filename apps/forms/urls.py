"""
URL configuration for forms app
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'forms'

# API Router
router = DefaultRouter()
router.register(r'templates', views.FormTemplateViewSet, basename='formtemplate')
router.register(r'forms', views.FormViewSet, basename='form')
router.register(r'fields', views.FormFieldViewSet, basename='formfield')
router.register(r'field-options', views.FormFieldOptionViewSet, basename='formfieldoption')
router.register(r'submissions', views.FormSubmissionViewSet, basename='formsubmission')
router.register(r'versions', views.FormVersionViewSet, basename='formversion')
router.register(r'shares', views.FormShareViewSet, basename='formshare')

urlpatterns = [
    # API endpoints
    path('api/', include(router.urls)),

    # Form submission endpoint
    path('api/forms/<slug:slug>/submit/', views.submit_form, name='submit_form'),

    # Web interface endpoints (to be implemented later)
    path('', views.form_list_view, name='form_list'),
    path('create/', views.form_create_view, name='form_create'),
    path('<slug:slug>/', views.form_detail_view, name='form_detail'),
    path('<slug:slug>/edit/', views.form_edit_view, name='form_edit'),
    path('<slug:slug>/builder/', views.form_builder_view, name='form_builder'),
    path('<slug:slug>/builder/debug/', views.form_builder_debug_view, name='form_builder_debug'),
    path('vue-test/', views.vue_test_view, name='vue_test'),
]
